import React, { useState, useEffect } from 'react';

// D<PERSON> liệu dinh dưỡng cho 100g mỗi loại thực phẩm
const foodData = {
  // Trứng và sản phẩm từ trứng
  'Trứng gà luộc': { calo: 155, protein: 13, carb: 1.1, fat: 11 },
  'Trứng chiên ít dầu': { calo: 196, protein: 13.6, carb: 0.7, fat: 15 },
  'Lòng trắng trứng': { calo: 52, protein: 11, carb: 0.7, fat: 0.2 },

  // Thịt và cá
  'Ức gà kho gừng': { calo: 165, protein: 31, carb: 0, fat: 3.6 },
  'Ức gà hấp': { calo: 165, protein: 31, carb: 0, fat: 3.6 },
  '<PERSON><PERSON> chiên nước mắm (không da)': { calo: 190, protein: 29, carb: 2, fat: 7 },
  'Thịt heo nạc luộc': { calo: 143, protein: 26, carb: 0, fat: 3.5 },
  'Thịt heo nạc bằm': { calo: 143, protein: 26, carb: 0, fat: 3.5 },
  'Thịt bò xào': { calo: 250, protein: 26, carb: 0, fat: 15 },
  'Bò kho': { calo: 200, protein: 25, carb: 3, fat: 10 },
  'Cá basa áp chảo': { calo: 120, protein: 20, carb: 0, fat: 4 },
  'Cá basa nướng giấy bạc': { calo: 90, protein: 19, carb: 0, fat: 1.5 },
  'Cá điêu hồng chiên áp chảo': { calo: 128, protein: 20, carb: 0, fat: 4.5 },
  'Cá hộp (cá nục sốt cà)': { calo: 158, protein: 25, carb: 4, fat: 5 },

  // Rau củ và trái cây
  'Khoai lang luộc': { calo: 86, protein: 1.6, carb: 20, fat: 0.1 },
  'Cơm trắng (gạo tẻ)': { calo: 130, protein: 2.7, carb: 28, fat: 0.3 },
  'Bánh mì nguyên cám': { calo: 247, protein: 13, carb: 41, fat: 4.2 },
  'Rau muống luộc': { calo: 18, protein: 2.6, carb: 3.1, fat: 0.2 },
  'Cải thìa luộc': { calo: 13, protein: 1.5, carb: 2.2, fat: 0.2 },
  'Cải ngọt xào': { calo: 20, protein: 2, carb: 3.5, fat: 0.3 },
  'Cải xanh luộc': { calo: 25, protein: 3, carb: 4, fat: 0.4 },
  'Đậu que luộc': { calo: 35, protein: 1.8, carb: 7, fat: 0.1 },
  'Bông cải luộc': { calo: 25, protein: 3, carb: 5, fat: 0.3 },
  'Dưa leo': { calo: 16, protein: 0.7, carb: 4, fat: 0.1 },
  'Dưa leo trộn chua ngọt': { calo: 25, protein: 0.7, carb: 6, fat: 0.1 },
  'Cà chua': { calo: 18, protein: 0.9, carb: 3.9, fat: 0.2 },

  // Trái cây
  'Chuối': { calo: 89, protein: 1.1, carb: 23, fat: 0.3 },
  'Cam': { calo: 47, protein: 0.9, carb: 12, fat: 0.1 },
  'Ổi': { calo: 68, protein: 2.6, carb: 14, fat: 1 },
  'Táo': { calo: 52, protein: 0.3, carb: 14, fat: 0.2 },
  'Thanh long': { calo: 60, protein: 1.2, carb: 13, fat: 0.4 },
  'Dưa hấu': { calo: 30, protein: 0.6, carb: 8, fat: 0.2 },
  'Đu đủ': { calo: 43, protein: 0.5, carb: 11, fat: 0.3 },

  // Sữa và đậu phụ
  'Sữa tươi không đường': { calo: 42, protein: 3.4, carb: 5, fat: 1 },
  'Đậu hũ non': { calo: 76, protein: 8, carb: 1.9, fat: 4.8 },
  'Đậu phụ hấp': { calo: 76, protein: 8, carb: 1.9, fat: 4.8 },

  // Canh và nước dùng
  'Canh bí đỏ nấu thịt bằm': { calo: 40, protein: 3.5, carb: 3.5, fat: 1.5 },
  'Canh bí xanh': { calo: 25, protein: 2, carb: 4, fat: 0.5 },
  'Canh cải thìa': { calo: 20, protein: 2, carb: 2.5, fat: 0.5 },
  'Canh mồng tơi nấu tôm khô': { calo: 30, protein: 4, carb: 2, fat: 1 },
  'Canh mướp nấu tôm': { calo: 35, protein: 5, carb: 2.5, fat: 1 },
  'Canh bí nấu tôm': { calo: 35, protein: 5, carb: 3, fat: 1 },
  'Canh rau dền': { calo: 25, protein: 3, carb: 2, fat: 0.5 },

  // Món xào và nấu
  'Bí đỏ xào với thịt bằm': { calo: 60, protein: 4, carb: 6, fat: 2.5 },
  'Đậu hũ non sốt cà': { calo: 90, protein: 8, carb: 5, fat: 5 },
};

const App = () => {
  const [selectedFood, setSelectedFood] = useState('');
  const [grams, setGrams] = useState(100);
  const [entries, setEntries] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  // Tải dữ liệu từ localStorage khi component mount
  useEffect(() => {
    const savedEntries = localStorage.getItem('foodEntries');
    if (savedEntries) {
      setEntries(JSON.parse(savedEntries));
    }
  }, []);

  // Lưu dữ liệu vào localStorage khi entries thay đổi
  useEffect(() => {
    localStorage.setItem('foodEntries', JSON.stringify(entries));
  }, [entries]);

  const addFood = () => {
    if (selectedFood && foodData[selectedFood] && grams > 0) {
      setEntries([...entries, {
        id: Date.now(),
        name: selectedFood,
        grams: grams
      }]);
      setSelectedFood('');
      setGrams(100);
      setSearchTerm('');
    }
  };

  const removeFood = (id) => {
    setEntries(entries.filter(entry => entry.id !== id));
  };

  const clearAll = () => {
    setEntries([]);
  };

  const exportData = () => {
    const exportText = `
🥗 THÔNG TIN DINH DƯỠNG THỰC ĐƠN

📋 Danh sách món ăn:
${entries.map(item => `• ${item.name}: ${item.grams}g`).join('\n')}

📊 Tổng dinh dưỡng:
• Calories: ${totals.calo.toFixed(1)} kcal
• Protein: ${totals.protein.toFixed(1)} g (${((totals.protein / totals.calo) * 100).toFixed(1)}% calories)
• Carbohydrate: ${totals.carb.toFixed(1)} g (${((totals.carb * 4 / totals.calo) * 100).toFixed(1)}% calories)
• Fat: ${totals.fat.toFixed(1)} g (${((totals.fat * 9 / totals.calo) * 100).toFixed(1)}% calories)

💡 Khuyến nghị: Protein 15-25%, Carbs 45-65%, Fat 20-35%
    `.trim();

    navigator.clipboard.writeText(exportText).then(() => {
      alert('Đã sao chép thông tin dinh dưỡng vào clipboard!');
    }).catch(() => {
      // Fallback cho trình duyệt không hỗ trợ clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = exportText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Đã sao chép thông tin dinh dưỡng vào clipboard!');
    });
  };

  // Lọc thực phẩm theo từ khóa tìm kiếm
  const filteredFoods = Object.keys(foodData).filter(food =>
    food.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totals = entries.reduce(
    (acc, item) => {
      const data = foodData[item.name];
      const factor = item.grams / 100;
      acc.calo += data.calo * factor;
      acc.protein += data.protein * factor;
      acc.carb += data.carb * factor;
      acc.fat += data.fat * factor;
      return acc;
    },
    { calo: 0, protein: 0, carb: 0, fat: 0 }
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h1 className="text-3xl font-bold text-center text-gray-800 mb-8">
            🥗 Tính Toán Dinh Dưỡng Thực Đơn
          </h1>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Phần thêm món ăn */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-700 mb-4">Thêm món ăn</h2>

              {/* Tìm kiếm */}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  Tìm kiếm món ăn:
                </label>
                <input
                  type="text"
                  className="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Nhập tên món ăn..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {/* Chọn món ăn */}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  Chọn món ăn:
                </label>
                <select
                  className="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={selectedFood}
                  onChange={(e) => setSelectedFood(e.target.value)}
                >
                  <option value="">-- Chọn món ăn --</option>
                  {filteredFoods.map((food) => (
                    <option key={food} value={food}>{food}</option>
                  ))}
                </select>
              </div>

              {/* Nhập số gram */}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  Khối lượng (gram):
                </label>
                <input
                  type="number"
                  className="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Nhập số gram"
                  value={grams}
                  onChange={(e) => setGrams(Number(e.target.value))}
                  min="1"
                />
              </div>

              {/* Hiển thị thông tin dinh dưỡng của món được chọn */}
              {selectedFood && foodData[selectedFood] && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-medium text-blue-800 mb-2">
                    Dinh dưỡng cho {grams}g {selectedFood}:
                  </h3>
                  <div className="grid grid-cols-2 gap-2 text-sm text-blue-700">
                    <div>Calories: {(foodData[selectedFood].calo * grams / 100).toFixed(1)} kcal</div>
                    <div>Protein: {(foodData[selectedFood].protein * grams / 100).toFixed(1)} g</div>
                    <div>Carbs: {(foodData[selectedFood].carb * grams / 100).toFixed(1)} g</div>
                    <div>Fat: {(foodData[selectedFood].fat * grams / 100).toFixed(1)} g</div>
                  </div>
                </div>
              )}

              <button
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 disabled:bg-gray-400"
                onClick={addFood}
                disabled={!selectedFood || grams <= 0}
              >
                ➕ Thêm món ăn
              </button>
            </div>

            {/* Phần hiển thị kết quả */}
            <div className="space-y-6">
              {/* Danh sách món đã chọn */}
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-gray-700">
                    Danh sách món ăn ({entries.length})
                  </h2>
                  {entries.length > 0 && (
                    <button
                      onClick={clearAll}
                      className="text-red-600 hover:text-red-800 text-sm font-medium"
                    >
                      🗑️ Xóa tất cả
                    </button>
                  )}
                </div>

                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {entries.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">
                      Chưa có món ăn nào được chọn
                    </p>
                  ) : (
                    entries.map((item) => (
                      <div key={item.id} className="flex justify-between items-center bg-gray-50 p-3 rounded-lg">
                        <div>
                          <div className="font-medium text-gray-800">{item.name}</div>
                          <div className="text-sm text-gray-600">{item.grams}g</div>
                        </div>
                        <button
                          onClick={() => removeFood(item.id)}
                          className="text-red-500 hover:text-red-700 p-1"
                        >
                          ❌
                        </button>
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* Tổng dinh dưỡng */}
              {entries.length > 0 && (
                <div className="bg-gradient-to-r from-green-100 to-blue-100 p-6 rounded-lg">
                  <h2 className="text-xl font-semibold text-gray-800 mb-4 text-center">
                    📊 Tổng Dinh Dưỡng
                  </h2>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {totals.calo.toFixed(1)}
                      </div>
                      <div className="text-sm text-gray-600">Calories (kcal)</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {totals.protein.toFixed(1)}
                      </div>
                      <div className="text-sm text-gray-600">Protein (g)</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {totals.carb.toFixed(1)}
                      </div>
                      <div className="text-sm text-gray-600">Carbs (g)</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-yellow-600">
                        {totals.fat.toFixed(1)}
                      </div>
                      <div className="text-sm text-gray-600">Fat (g)</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Thông tin bổ sung */}
              {entries.length > 0 && (
                <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                  <h3 className="font-medium text-yellow-800 mb-2">💡 Gợi ý dinh dưỡng:</h3>
                  <div className="text-sm text-yellow-700 space-y-1">
                    <div>• Protein: {((totals.protein / totals.calo) * 100).toFixed(1)}% tổng calories</div>
                    <div>• Carbs: {((totals.carb * 4 / totals.calo) * 100).toFixed(1)}% tổng calories</div>
                    <div>• Fat: {((totals.fat * 9 / totals.calo) * 100).toFixed(1)}% tổng calories</div>
                    <div className="mt-2 pt-2 border-t border-yellow-300">
                      <strong>Khuyến nghị:</strong> Protein 15-25%, Carbs 45-65%, Fat 20-35%
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="mt-8 pt-6 border-t border-gray-200 text-center text-sm text-gray-500">
            <p>📱 Ứng dụng tính toán dinh dưỡng thực đơn cá nhân</p>
            <p className="mt-1">Dữ liệu được lưu tự động trên trình duyệt của bạn</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;
