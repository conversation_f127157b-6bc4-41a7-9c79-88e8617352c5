import React, { useState } from 'react';

const foodData = {
  'Trứng gà luộc': { calo: 143, protein: 13, carb: 1.1, fat: 10.3 },
  '<PERSON><PERSON><PERSON> lang luộc': { calo: 86, protein: 1.6, carb: 20, fat: 0.1 },
  '<PERSON><PERSON><PERSON> tươi không đường': { calo: 42, protein: 3.4, carb: 5, fat: 1 },
  'Ức gà kho gừng': { calo: 165, protein: 31, carb: 0, fat: 3.6 },
  '<PERSON><PERSON> muống luộc': { calo: 18, protein: 2, carb: 3.1, fat: 0.2 },
  'Cơm trắng': { calo: 130, protein: 2.5, carb: 28, fat: 0.3 },
  '<PERSON><PERSON> bí đỏ nấu thịt bằm': { calo: 40, protein: 3.5, carb: 3.5, fat: 1.5 },
  'Chuối': { calo: 89, protein: 1.1, carb: 23, fat: 0.3 },
  '<PERSON><PERSON> basa áp chảo': { calo: 120, protein: 20, carb: 0, fat: 4 },
  '<PERSON><PERSON><PERSON> th<PERSON><PERSON> lu<PERSON>': { calo: 13, protein: 1.5, carb: 2.2, fat: 0.2 },
  '<PERSON> hoặc ổi': { calo: 52, protein: 1, carb: 13, fat: 0.2 },
};

const App = () => {
  const [selectedFood, setSelectedFood] = useState('');
  const [grams, setGrams] = useState(100);
  const [entries, setEntries] = useState([]);

  const addFood = () => {
    if (selectedFood && foodData[selectedFood]) {
      setEntries([...entries, { name: selectedFood, grams }]);
    }
  };

  const totals = entries.reduce(
    (acc, item) => {
      const data = foodData[item.name];
      const factor = item.grams / 100;
      acc.calo += data.calo * factor;
      acc.protein += data.protein * factor;
      acc.carb += data.carb * factor;
      acc.fat += data.fat * factor;
      return acc;
    },
    { calo: 0, protein: 0, carb: 0, fat: 0 }
  );

  return (
    <div className="p-4 max-w-md mx-auto">
      <h1 className="text-xl font-bold mb-4">Tính dinh dưỡng món ăn</h1>
      <select
        className="w-full mb-2 border rounded p-2"
        value={selectedFood}
        onChange={(e) => setSelectedFood(e.target.value)}
      >
        <option value="">-- Chọn món ăn --</option>
        {Object.keys(foodData).map((food) => (
          <option key={food} value={food}>{food}</option>
        ))}
      </select>
      <input
        type="number"
        className="w-full mb-2 border rounded p-2"
        placeholder="Số gram"
        value={grams}
        onChange={(e) => setGrams(Number(e.target.value))}
      />
      <button
        className="bg-blue-500 text-white px-4 py-2 rounded"
        onClick={addFood}
      >
        Thêm món
      </button>

      <div className="mt-4">
        <h2 className="font-semibold">Danh sách món đã chọn:</h2>
        <ul className="list-disc ml-5">
          {entries.map((item, index) => (
            <li key={index}>{item.name} - {item.grams}g</li>
          ))}
        </ul>

        <h2 className="font-semibold mt-4">Tổng dinh dưỡng:</h2>
        <p>Calories: {totals.calo.toFixed(1)} kcal</p>
        <p>Protein: {totals.protein.toFixed(1)} g</p>
        <p>Carbohydrate: {totals.carb.toFixed(1)} g</p>
        <p>Fat: {totals.fat.toFixed(1)} g</p>
      </div>
    </div>
  );
};

export default App;
